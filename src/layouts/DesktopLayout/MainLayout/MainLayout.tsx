import FeatureList from '@/components/Desktop/Home/FeatureList'
import { HomeFaculties } from '@/components/Desktop/Home/HomeFaculties/HomeFaculties'
import ResearchInfo from '@/components/Desktop/Home/ResearchInfo'
import SearchMedicineByImage from '@/components/Desktop/Home/SearchMedicineByImage'
import { HomeDidYouKnow } from '@/features/home/<USER>/HomeDidYouKnow/HomeDidYouKnow'
import {
  getBodyPartsCache,
  getHomeFacultiesCache,
  getNewestVersionCache,
  getRandomFactsCache,
  getRandomKeywordsCache,
  getRandomSearchTipsCache,
  getSubscriptionPlansCache,
} from '@/features/home/<USER>/Home/HomeContainer'
import { getLocale } from 'next-intl/server'

const MainLayout: React.FC = async () => {
  const locale = await getLocale()

  const [_, faculties, bodyParts, __, randomKeywords, randomFacts, randomSearchTips] =
    await Promise.all([
      getNewestVersionCache(),
      getHomeFacultiesCache(locale),
      getBodyPartsCache(locale),
      getSubscriptionPlansCache(),
      getRandomKeywordsCache(),
      getRandomFactsCache(locale),
      getRandomSearchTipsCache(locale),
    ])

  return (
    <div className="flex max-w-full gap-6 overflow-hidden bg-custom-background-hover px-16 py-6">
      <div className="inline-flex min-w-0 flex-[7] flex-col gap-3 rounded-3xl bg-white p-4">
        <ResearchInfo searchTips={[]} />
        <FeatureList />
        <HomeFaculties faculties={faculties?.docs ?? []} />
      </div>

      <div className="inline-flex h-fit min-w-0 flex-[3] flex-col gap-3 rounded-3xl bg-white p-4">
        <SearchMedicineByImage />
        <HomeDidYouKnow classStyleSection="px-0" fact={randomFacts?.docs?.[0] ?? null} />
      </div>
    </div>
  )
}

export default MainLayout

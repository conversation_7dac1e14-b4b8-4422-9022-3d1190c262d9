import eclipse from '@/assets/icons/eclipse-decor-2.svg'
import lightBulb from '@/assets/icons/white-light-bulb.svg'
import { Fact } from '@/payload-types'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { Section } from '../Section/Section'
interface HomeDidYouKnowProps {
  fact: Fact | null
  classStyleSection?: string
}
export const HomeDidYouKnow: React.FC<HomeDidYouKnowProps> = ({ fact, classStyleSection }) => {
  const t = useTranslations()
  if (!fact) return null

  return (
    <Section className={classStyleSection}>
      <div
        style={{
          background: 'linear-gradient(58.37deg, #8C82FA 23.78%, #C180FF 109.22%)',
        }}
        className="relative overflow-hidden rounded-lg p-4 text-white"
      >
        <Image
          src={eclipse}
          alt="eclipse"
          width={148}
          height={148}
          className="absolute left-[-45px] top-[-30px] h-[148px] w-[148px] rotate-[5deg]"
        ></Image>
        <div className="mb-3 flex gap-x-2">
          <Image
            src={lightBulb}
            alt="light-bulb"
            width={20}
            height={20}
            className="size-5 shrink-0"
          ></Image>
          <p className="typo-body-3">{t('MES-576')}</p>
        </div>
        <p className="typo-body-7">{fact?.content}</p>
      </div>
    </Section>
  )
}

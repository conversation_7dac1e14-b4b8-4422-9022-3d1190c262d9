import ReCaptcha from '@/components/ReCaptcha/ReCaptcha'
import { HOME_MAIN_ELEMENT_ID, REVALIDATE_TAGS } from '@/constants/global.constant'
import { bodyPartService } from '@/services/body-part.service'
import { factService } from '@/services/fact.service'
import { facultyService } from '@/services/faculty.service'
import { keywordService } from '@/services/keyword.service'
import { searchTipService } from '@/services/search-tip.service'
import { subscriptionService } from '@/services/subscription.service'
import { versionService } from '@/services/version.service'
import { getLocale } from 'next-intl/server'
import { cache } from 'react'
import { HomeDailyVocabulary } from '../../components/HomeDailyVocabulary/HomeDailyVocabulary'
import { HomeDidYouKnow } from '../../components/HomeDidYouKnow/HomeDidYouKnow'
import { HomeFaculties } from '../../components/HomeFaculties/HomeFaculties'
import { HomeFeaturedCategories } from '../../components/HomeFeaturedCategories/HomeFeaturedCategories'
import { HomeMedicines } from '../../components/HomeMedicines/HomeMedicines'
import { HomePosts } from '../../components/HomePosts/HomePosts'
import { HomeSearch } from '../../components/HomeSearch/HomeSearch'
import { HomeSearchMedicineByImage } from '../../components/HomeSearchMedicineByImage/HomeSearchMedicineByImage'
import { HomeSubscribe } from '../../components/HomeSubscribe/HomeSubscribe'

import { LivestreamPopup } from '@/components/Popup/LivestreamPopup/LivestreamPopup'
import { HomeContainerClient } from './HomeContainer.client'

export const HomeContainer = async () => {
  const locale = await getLocale()

  const [_, faculties, bodyParts, __, randomKeywords, randomFacts, randomSearchTips] =
    await Promise.all([
      getNewestVersionCache(),
      getHomeFacultiesCache(locale),
      getBodyPartsCache(locale),
      getSubscriptionPlansCache(),
      getRandomKeywordsCache(),
      getRandomFactsCache(locale),
      getRandomSearchTipsCache(locale),
    ])

  return (
    <>
      <HomeContainerClient></HomeContainerClient>

      <main className="space-y-8 overflow-hidden pt-4" id={HOME_MAIN_ELEMENT_ID}>
        <HomeSearch searchTips={randomSearchTips?.docs ?? []} />

        <HomeSearchMedicineByImage></HomeSearchMedicineByImage>

        <HomeFeaturedCategories></HomeFeaturedCategories>

        <HomeDailyVocabulary keywords={randomKeywords?.docs ?? []}></HomeDailyVocabulary>

        <HomeDidYouKnow fact={randomFacts?.docs?.[0] ?? null}></HomeDidYouKnow>

        <HomeFaculties faculties={faculties?.docs ?? []}></HomeFaculties>

        <HomeMedicines bodyParts={bodyParts ?? null}></HomeMedicines>

        <HomePosts></HomePosts>

        {/* <HomeSubscriptions
          subscriptionPlansData={subscriptionPlans ?? null}
          newestVersionData={newestVersion ?? null}
        ></HomeSubscriptions> */}
        {/* Subcribe */}
        <ReCaptcha>
          <HomeSubscribe />
        </ReCaptcha>

        {/* Livestream Popup */}
        <LivestreamPopup boundsElementId={HOME_MAIN_ELEMENT_ID}></LivestreamPopup>
      </main>
    </>
  )
}

export const getNewestVersionCache = cache(async () => {
  try {
    const newestVersion = await versionService.getNewestAppVersion()
    return newestVersion
  } catch (error) {
    console.log(error)
    return null
  }
})
export const getSubscriptionPlansCache = cache(async () => {
  try {
    const subscriptionPlans = await subscriptionService.getSubscriptionPlans({
      params: {
        sort: 'price',
      },
    })
    return subscriptionPlans
  } catch (error) {
    console.log(error)
    return null
  }
})
// const getHomeConfigCache = cache(async () => {
//   const cookieStore = await cookies()
//   const token = cookieStore.get(PORTAL_TOKEN_KEY)?.value
//   const sessionToken = cookieStore.get(LOGIN_SESSION_COOKIE_KEY)?.value
//   try {
//     const homePageConfig = await homePageConfigService.getHomePageConfig({
//       params: {
//         locale: 'all',
//         depth: 3,
//       },
//       options: {
//         next: {
//           // revalidate: 60,
//           // tags: ['posts'],
//         },
//         cache: 'no-store',
//         headers: {
//           Cookie: `${PORTAL_TOKEN_KEY}=${token}; ${LOGIN_SESSION_COOKIE_KEY}=${sessionToken}`,
//         },
//       },
//     })
//     return homePageConfig || null
//   } catch (error) {
//     console.log(error)
//   }
// })

export const getHomeFacultiesCache = cache(async (locale: string) => {
  try {
    const faculties = await facultyService.getHomeFaculties({
      params: {
        locale: locale,
        limit: 50,
        sort: ['-hasQuestions', 'name'],
      },
      options: {
        next: {
          revalidate: 300,
        },
        cache: 'default',
      },
    })
    return faculties
  } catch (error) {
    console.log(error)
    return null
  }
})

export const getBodyPartsCache = cache(async (locale: string) => {
  try {
    const bodyParts = await bodyPartService.getBodyParts({
      params: {
        locale: locale,
        limit: 50,
        depth: 1,
        select: {
          name: true,
          id: true,
          heroImage: true,
        },
        where: {
          rootBodyPart: {
            equals: true,
          },
        },
      },
      options: {
        next: {
          revalidate: 300,
        },
        cache: 'default',
      },
    })
    return bodyParts
  } catch (error) {
    console.log(error)
    return null
  }
})
export const getRandomKeywordsCache = cache(async () => {
  try {
    // const cookieStore = await cookies()
    // const token = cookieStore.get(PORTAL_TOKEN_KEY)?.value
    // const sessionToken = cookieStore.get(LOGIN_SESSION_COOKIE_KEY)?.value
    const randomKeywords = await keywordService.getRandomKeywords({
      params: {
        limit: 5,
        locale: 'all',
        select: {
          name: true,
          id: true,
          hiragana: true,
          audio: true,
          relatedImages: true,
          description: true,
        },
      },
      options: {
        next: {
          revalidate: 60 * 60 * 12,
        },
        cache: 'default',
        // headers: {
        //   Cookie: `${PORTAL_TOKEN_KEY}=${token}; ${LOGIN_SESSION_COOKIE_KEY}=${sessionToken}`,
        // },
      },
    })
    return randomKeywords
  } catch (error) {
    console.log(error)
    return null
  }
})

export const getRandomFactsCache = cache(async (locale: string) => {
  try {
    const randomFacts = await factService.getRandomFacts({
      params: {
        limit: 1,
        locale: locale,
      },
      options: {
        next: {
          revalidate: 60 * 60 * 12,
          tags: [REVALIDATE_TAGS.FACTS],
        },
        cache: 'default',
      },
    })
    return randomFacts
  } catch (error) {
    console.log(error)
    return null
  }
})

export const getRandomSearchTipsCache = cache(async (locale: string) => {
  try {
    const randomSearchTips = await searchTipService.getRandomSearchTips({
      params: {
        limit: 3,
        locale: locale,
      },
      options: {
        next: {
          revalidate: 60 * 60 * 12,
          tags: [REVALIDATE_TAGS.SEARCH_TIPS],
        },
        cache: 'default',
      },
    })
    return randomSearchTips
  } catch (error) {
    console.log(error)
    return null
  }
})

'use client'
import { StarIcon } from '@/components/Icons/StarIcon'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import { SearchTip } from '@/payload-types'
import { cn } from '@/utilities/cn'
import { useTranslations } from 'next-intl'
import React from 'react'

interface SearchTipProps {
  tips: SearchTip[]
  classText?: string
}

export const SearchTips: React.FC<SearchTipProps> = ({ tips, classText = 'typo-body-6' }) => {
  const t = useTranslations()

  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem
        value={'home-tips'}
        className="rounded-b-[12px] border-none bg-white p-3 shadow-[0px_4px_22px_0px_#00000026]"
      >
        <AccordionTrigger className={cn('p-0', '!no-underline')}>
          <>
            <h3 className={`${classText} flex items-center gap-x-2 text-primary`}>
              {t('MES-555')}
              <StarIcon width={16} height={16}></StarIcon>
            </h3>
          </>
        </AccordionTrigger>

        <AccordionContent className="typo-body-7 py-3 text-subdued">
          <div className="hide-scroll flex snap-x snap-mandatory overflow-x-auto">
            <div className="flex gap-2">
              {tips?.map((tip, index) => {
                return (
                  <div
                    key={tip?.id || index}
                    className="flex w-[272px] shrink-0 snap-start rounded-lg"
                  >
                    <div className="w-full overflow-hidden rounded-lg border bg-white">
                      <div className="typo-body-6 items-start justify-between gap-x-2 p-3 text-start text-base-default !no-underline">
                        {tip?.title}
                      </div>
                      <div className="typo-body-7 px-3 pb-3 text-subdued">{tip?.description}</div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )
}

/* <div
key={tip?.id || index}
className="flex w-[272px] shrink-0 snap-start rounded-lg"
>
<Accordion type="single" collapsible className="w-full">
  <AccordionItem
    value={tip?.id || index.toString()}
    className="overflow-hidden rounded-lg border bg-white"
  >
    <AccordionTrigger className="typo-body-6 items-start justify-between gap-x-2 p-3 text-start text-base-default !no-underline">
      {localizedTitle?.[locale]}
    </AccordionTrigger>
    <AccordionContent className="typo-body-7 px-3 pb-3 text-subdued">
      {localizedDescription?.[locale]}
    </AccordionContent>
  </AccordionItem>
</Accordion>
</div> */

import { StaticImport } from 'next/dist/shared/lib/get-img-props'
import Image from 'next/image'

type FeatureBoxType = {
  text: string
  source: string | StaticImport
  alt: string
  height?: number
  width?: number
}

const FeatureBox: React.FC<FeatureBoxType> = ({ text, source, alt, height = 30, width = 30 }) => {
  return (
    <div className="flex flex-col items-center justify-center gap-2 rounded-lg border border-neutral-100 px-1 py-4">
      <div className="rounded-md bg-primary-50 p-3">
        <Image src={source} alt={alt} height={height} width={width} />
      </div>

      <span className="typo-body-10">{text}</span>
    </div>
  )
}

export default FeatureBox

import { Section } from '@/features/home/<USER>/Section/Section'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import FeatureBox from './FeatureBox'

// image
import InsuranceIcon from '@/assets/icons/insurance.svg'
import HospitalIcon from '@/assets/icons/hospital.svg'

const FeatureList: React.FC = () => {
  const t = useTranslations()
  return (
    <Section className="mt-2 px-0">
      <div className="typo-heading-8 flex items-center gap-2 text-primary-500">
        {t('MES-640')}

        <Image src={InsuranceIcon} alt="insurance-icon" height={24} width={24} />
      </div>

      <div className="mt-3 grid grid-cols-4 gap-3">
        <FeatureBox text="Tìm phòng, khoa khám" source={HospitalIcon} alt="hospital icon" />
      </div>
    </Section>
  )
}

export default FeatureList
